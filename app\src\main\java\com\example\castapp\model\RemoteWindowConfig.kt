package com.example.castapp.model

import android.content.Context
import android.graphics.RectF
import androidx.core.content.edit
import com.example.castapp.utils.AppLog
import com.google.gson.Gson
import java.util.concurrent.ConcurrentHashMap

/**
 * 🎯 统一的窗口参数管理类
 * 用于在遥控端统一管理所有窗口参数，避免分散存储和复杂收集
 */
data class RemoteWindowConfig(
    // 基础信息
    val connectionId: String,
    val ipAddress: String,
    val port: Int,
    val isActive: Boolean,
    val deviceName: String?,
    val note: String?,

    // 🎯 新增：文字窗口内容数据（集中管理）
    var textContent: String = "",
    var richTextData: String? = null,
    var isBold: Boolean = false,
    var isItalic: Boolean = false,
    var fontSize: Int = 13,
    var fontName: String? = null,
    var fontFamily: String? = null,
    var lineSpacing: Float = 0.0f,
    var textAlignment: Int = android.view.Gravity.CENTER,
    var isWindowTextColorEnabled: Boolean = false,
    var windowTextBackgroundColor: Int = 0xFFFFFFFF.toInt(),

    // 位置和变换参数（实时更新）
    var positionX: Float,
    var positionY: Float,
    var scaleFactor: Float,
    var rotationAngle: Float,
    val zOrder: Int,

    // 功能开关
    var isCropping: Boolean,
    val isDragEnabled: Boolean,
    val isScaleEnabled: Boolean,
    val isRotationEnabled: Boolean,
    var isVisible: Boolean,
    var isMirrored: Boolean,

    // 样式参数（实时更新）
    var cornerRadius: Float,
    var alpha: Float,
    var isBorderEnabled: Boolean,
    var borderColor: Int,
    var borderWidth: Float,

    // 控制参数
    val isControlEnabled: Boolean,
    val isEditEnabled: Boolean,

    // 窗口尺寸
    var baseWindowWidth: Int,
    var baseWindowHeight: Int,

    // 背景和模式
    val windowColorEnabled: Boolean,
    val windowBackgroundColor: Int,
    val isLandscapeModeEnabled: Boolean,

    // 裁剪参数
    var cropRectRatio: RectF? = null,

    // 🎯 新增：数据来源和时间戳
    val dataSource: String = "unknown", // 数据来源：receiver/cache/visualization
    val lastUpdated: Long = System.currentTimeMillis() // 最后更新时间
) {
    
    /**
     * 🔄 从CastWindowInfo创建统一参数
     */
    companion object {
        fun fromCastWindowInfo(windowInfo: CastWindowInfo, context: android.content.Context? = null): RemoteWindowConfig {
            // 🎯 关键修复：对于文字窗口，从SharedPreferences中读取保存的文字内容和格式
            var textContent = ""
            var richTextData: String? = null
            var isBold = false
            var isItalic = false
            var fontSize = 13
            var fontName: String? = null
            var fontFamily: String? = null
            var lineSpacing = 0.0f
            var textAlignment = android.view.Gravity.CENTER
            var isWindowTextColorEnabled = false
            var windowTextBackgroundColor = 0xFFFFFFFF.toInt()

            val isTextWindow = windowInfo.connectionId.startsWith("text_")
            if (isTextWindow && context != null) {
                try {
                    val textFormatManager = com.example.castapp.utils.TextFormatManager(context)

                    // 优先尝试获取富文本格式
                    val richTextSpannable = textFormatManager.getRichTextFormat(windowInfo.connectionId)
                    if (richTextSpannable != null) {
                        textContent = richTextSpannable.toString()
                        richTextData = textFormatManager.serializeSpannableString(richTextSpannable)
                        AppLog.d("【统一配置管理器】📝 已从SharedPreferences恢复富文本格式: ${windowInfo.connectionId}, 内容='$textContent'")
                    } else {
                        // 后备方案：获取基本格式
                        val formatInfo = textFormatManager.getTextFormat(windowInfo.connectionId)
                        if (formatInfo != null) {
                            textContent = formatInfo.textContent
                            isBold = formatInfo.isBold
                            isItalic = formatInfo.isItalic
                            fontSize = formatInfo.fontSize
                            AppLog.d("【统一配置管理器】📝 已从SharedPreferences恢复基本格式: ${windowInfo.connectionId}, 内容='$textContent'")
                        } else {
                            AppLog.d("【统一配置管理器】📝 未找到保存的文字格式: ${windowInfo.connectionId}")
                        }
                    }

                    // 尝试获取扩展格式信息
                    val extendedFormat = textFormatManager.getExtendedTextFormat(windowInfo.connectionId)
                    if (extendedFormat != null) {
                        fontName = extendedFormat.fontName
                        fontFamily = extendedFormat.fontFamily
                        lineSpacing = extendedFormat.lineSpacing
                        textAlignment = extendedFormat.textAlignment
                        AppLog.d("【统一配置管理器】📝 已恢复扩展格式信息: 字体=$fontName, 行间距=${lineSpacing}dp, 对齐=$textAlignment")
                    }
                } catch (e: Exception) {
                    AppLog.e("【统一配置管理器】📝 恢复文字格式失败: ${windowInfo.connectionId}", e)
                }
            }

            return RemoteWindowConfig(
                connectionId = windowInfo.connectionId,
                ipAddress = windowInfo.ipAddress,
                port = windowInfo.port,
                isActive = windowInfo.isActive,
                deviceName = windowInfo.deviceName,
                note = windowInfo.note,
                // 🎯 添加文字内容和格式信息
                textContent = textContent,
                richTextData = richTextData,
                isBold = isBold,
                isItalic = isItalic,
                fontSize = fontSize,
                fontName = fontName,
                fontFamily = fontFamily,
                lineSpacing = lineSpacing,
                textAlignment = textAlignment,
                isWindowTextColorEnabled = isWindowTextColorEnabled,
                windowTextBackgroundColor = windowTextBackgroundColor,
                positionX = windowInfo.positionX,
                positionY = windowInfo.positionY,
                scaleFactor = windowInfo.scaleFactor,
                rotationAngle = windowInfo.rotationAngle,
                zOrder = windowInfo.zOrder,
                isCropping = windowInfo.isCropping,
                isDragEnabled = windowInfo.isDragEnabled,
                isScaleEnabled = windowInfo.isScaleEnabled,
                isRotationEnabled = windowInfo.isRotationEnabled,
                isVisible = windowInfo.isVisible,
                isMirrored = windowInfo.isMirrored,
                cornerRadius = windowInfo.cornerRadius,
                alpha = windowInfo.alpha,
                isBorderEnabled = windowInfo.isBorderEnabled,
                borderColor = windowInfo.borderColor,
                borderWidth = windowInfo.borderWidth,
                isControlEnabled = windowInfo.isControlEnabled,
                isEditEnabled = windowInfo.isEditEnabled,
                baseWindowWidth = windowInfo.baseWindowWidth,
                baseWindowHeight = windowInfo.baseWindowHeight,
                windowColorEnabled = windowInfo.windowColorEnabled,
                windowBackgroundColor = windowInfo.windowBackgroundColor,
                isLandscapeModeEnabled = windowInfo.isLandscapeModeEnabled,
                cropRectRatio = windowInfo.cropRectRatio
            )
        }
    }
    
    /**
     * 🔄 转换为批量同步消息的数据格式
     */
    fun toBatchSyncData(): Map<String, Any> {
        val data = mutableMapOf<String, Any>(
            "connectionId" to connectionId,
            "ipAddress" to ipAddress,
            "port" to port,
            "isActive" to isActive,
            "deviceName" to (deviceName ?: "未知设备"),
            "note" to (note ?: "无"),
            "positionX" to positionX,
            "positionY" to positionY,
            "scaleFactor" to scaleFactor,
            "rotationAngle" to rotationAngle,
            "zOrder" to zOrder,
            "isCropping" to isCropping,
            "isDragEnabled" to isDragEnabled,
            "isScaleEnabled" to isScaleEnabled,
            "isRotationEnabled" to isRotationEnabled,
            "isVisible" to isVisible,
            "isMirrored" to isMirrored,
            "cornerRadius" to cornerRadius,
            "alpha" to alpha,
            "isControlEnabled" to isControlEnabled,
            "isEditEnabled" to isEditEnabled,
            "isBorderEnabled" to isBorderEnabled,
            "borderColor" to borderColor,
            "borderWidth" to borderWidth,
            "baseWindowWidth" to baseWindowWidth,
            "baseWindowHeight" to baseWindowHeight,
            "windowColorEnabled" to windowColorEnabled,
            "windowBackgroundColor" to windowBackgroundColor,
            "isLandscapeModeEnabled" to isLandscapeModeEnabled
        )

        // 🎯 关键修复：添加文字窗口相关字段
        if (connectionId.startsWith("text_")) {
            data["textContent"] = textContent ?: ""
            data["richTextData"] = richTextData ?: ""
            data["isBold"] = isBold
            data["isItalic"] = isItalic
            data["fontSize"] = fontSize
            data["fontName"] = fontName ?: ""
            data["fontFamily"] = fontFamily ?: ""
            data["lineSpacing"] = lineSpacing
            data["textAlignment"] = textAlignment
            data["isWindowTextColorEnabled"] = isWindowTextColorEnabled
            data["windowTextBackgroundColor"] = windowTextBackgroundColor
            AppLog.d("【统一配置管理器】📝 批量同步数据包含文字内容: $connectionId, 内容='$textContent'")
        }

        // 添加裁剪区域信息（如果存在）
        cropRectRatio?.let { cropRatio ->
            data["cropRectRatio"] = mapOf(
                "left" to cropRatio.left,
                "top" to cropRatio.top,
                "right" to cropRatio.right,
                "bottom" to cropRatio.bottom
            )
        }

        return data
    }
}

/**
 * 🎯 统一的遥控端窗口配置管理器
 * 集中管理所有接收端的窗口参数，避免数据分散和不一致
 */
class RemoteWindowConfigManager private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: RemoteWindowConfigManager? = null

        fun getInstance(): RemoteWindowConfigManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteWindowConfigManager().also { INSTANCE = it }
            }
        }

        private const val PREFS_NAME = "remote_window_config"
        private const val KEY_PREFIX = "config_"
    }

    // 🎯 每个接收端的窗口配置映射：receiverId -> (connectionId -> RemoteWindowConfig)
    private val receiverConfigs = ConcurrentHashMap<String, ConcurrentHashMap<String, RemoteWindowConfig>>()

    private val gson = Gson()

    /**
     * 🎯 从接收端数据更新窗口配置（主要数据源）
     */
    fun updateFromReceiverData(receiverId: String, windowInfoList: List<CastWindowInfo>, context: android.content.Context? = null) {
        AppLog.d("【统一配置管理器】从接收端更新窗口配置: $receiverId, ${windowInfoList.size} 个窗口")

        val receiverConfigMap = receiverConfigs.getOrPut(receiverId) { ConcurrentHashMap() }

        // 清除旧配置
        receiverConfigMap.clear()

        // 添加新配置
        windowInfoList.forEach { windowInfo ->
            val config = RemoteWindowConfig.fromCastWindowInfo(windowInfo, context).copy(
                dataSource = "receiver",
                lastUpdated = System.currentTimeMillis()
            )
            receiverConfigMap[windowInfo.connectionId] = config
            AppLog.d("【统一配置管理器】更新窗口配置: ${windowInfo.connectionId}")
        }

        AppLog.d("【统一配置管理器】接收端数据更新完成: $receiverId")
    }

    /**
     * 🎯 从可视化组件同步实时参数
     */
    fun syncVisualizationParams(receiverId: String, visualizationDataList: List<WindowVisualizationData>) {
        AppLog.d("【统一配置管理器】同步可视化参数: $receiverId, ${visualizationDataList.size} 个窗口")

        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        visualizationDataList.forEach { visualData ->
            receiverConfigMap[visualData.connectionId]?.let { config ->
                // 🎯 关键修复：所有窗口都需要进行坐标转换，包括裁剪窗口
                // 遥控端和接收端的坐标系不同，必须进行转换
                val actualPositionX = visualData.visualizedX / visualData.remoteControlScale.toFloat()
                val actualPositionY = visualData.visualizedY / visualData.remoteControlScale.toFloat()

                if (visualData.isCropping) {
                    AppLog.d("【统一配置管理器】🎯 裁剪窗口坐标转换: ${visualData.connectionId}")
                    AppLog.d("  遥控端位置: (${visualData.visualizedX}, ${visualData.visualizedY})")
                    AppLog.d("  转换后位置: (${actualPositionX}, ${actualPositionY})")
                    AppLog.d("  缩放比例: ${visualData.remoteControlScale}")
                } else {
                    AppLog.d("【统一配置管理器】🎯 非裁剪窗口坐标转换: ${visualData.connectionId}, 转换后位置=(${actualPositionX}, ${actualPositionY})")
                }

                // 更新实时参数
                config.positionX = actualPositionX
                config.positionY = actualPositionY
                config.scaleFactor = visualData.scaleFactor
                config.rotationAngle = visualData.rotationAngle
                config.alpha = visualData.alpha
                config.isVisible = visualData.isVisible
                config.isMirrored = visualData.isMirrored
                config.cornerRadius = visualData.cornerRadius
                config.isBorderEnabled = visualData.isBorderEnabled
                config.borderColor = visualData.borderColor
                config.borderWidth = visualData.borderWidth

                // 🎯 关键修复：同步层级信息
                config.zOrder = visualData.zOrder
                AppLog.d("【统一配置管理器】🎯 同步层级信息: ${visualData.connectionId}, zOrder=${visualData.zOrder}")

                // 🎯 关键修复：同步窗口尺寸信息（需要转换坐标系）
                // 将遥控端可视化尺寸转换为接收端实际尺寸
                val actualWidth = (visualData.originalWidth / visualData.remoteControlScale).toInt()
                val actualHeight = (visualData.originalHeight / visualData.remoteControlScale).toInt()
                config.baseWindowWidth = actualWidth
                config.baseWindowHeight = actualHeight

                // 🎯 关键修复：同步裁剪参数
                config.isCropping = visualData.isCropping
                config.cropRectRatio = visualData.cropRectRatio
                AppLog.d("【统一配置管理器】🎯 同步裁剪参数: ${visualData.connectionId}, isCropping=${visualData.isCropping}, cropRectRatio=${visualData.cropRectRatio}")

                // 🎯 关键修复：对于文字窗口，同步文字内容和格式信息
                if (visualData.connectionId.startsWith("text_")) {
                    config.textContent = visualData.textContent
                    config.richTextData = visualData.richTextData
                    config.isBold = visualData.isBold
                    config.isItalic = visualData.isItalic
                    config.fontSize = visualData.fontSize
                    config.fontName = visualData.fontName
                    config.fontFamily = visualData.fontFamily
                    config.lineSpacing = visualData.lineSpacing
                    config.textAlignment = visualData.textAlignment

                    // 🎯 关键修复：窗色信息优先使用统一配置管理器中的最新数据，而不是可视化数据
                    // 因为可视化数据可能不是最新的窗色状态
                    AppLog.d("【统一配置管理器】🎯 窗色同步: 可视化数据中的窗色状态 - 启用=${visualData.isWindowColorEnabled}, 颜色=${String.format("#%08X", visualData.windowBackgroundColor)}")
                    AppLog.d("【统一配置管理器】🎯 窗色同步: 统一配置中的窗色状态 - 启用=${config.isWindowTextColorEnabled}, 颜色=${String.format("#%08X", config.windowTextBackgroundColor)}")

                    // 只有当可视化数据中的窗色信息确实有效时才更新
                    if (visualData.isWindowColorEnabled || visualData.windowBackgroundColor != 0xFFFFFFFF.toInt()) {
                        config.isWindowTextColorEnabled = visualData.isWindowColorEnabled
                        config.windowTextBackgroundColor = visualData.windowBackgroundColor
                        AppLog.d("【统一配置管理器】🎯 窗色已从可视化数据更新")
                    } else {
                        AppLog.d("【统一配置管理器】🎯 保留统一配置中的窗色状态（可视化数据为默认值）")
                    }

                    AppLog.d("【统一配置管理器】📝 同步文字内容: ${visualData.connectionId}, 内容='${visualData.textContent}'")
                }

                AppLog.d("【统一配置管理器】同步可视化参数: ${visualData.connectionId}")
                AppLog.d("  🎯 位置转换: 可视化位置(${visualData.visualizedX}, ${visualData.visualizedY}) -> 接收端位置($actualPositionX, $actualPositionY)")
                AppLog.d("  🎯 尺寸转换: 遥控端尺寸(${visualData.originalWidth}x${visualData.originalHeight}) ÷ ${visualData.remoteControlScale} -> 接收端尺寸(${actualWidth}x${actualHeight})")
                AppLog.d("  缩放: ${visualData.scaleFactor}, 旋转: ${visualData.rotationAngle}°")
            }
        }

    }

    /**
     * 🎯 更新单个窗口参数
     */
    fun updateWindowConfig(receiverId: String, connectionId: String, updateAction: (RemoteWindowConfig) -> RemoteWindowConfig) {
        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        receiverConfigMap[connectionId]?.let { currentConfig ->
            val updatedConfig = updateAction(currentConfig).copy(lastUpdated = System.currentTimeMillis())
            receiverConfigMap[connectionId] = updatedConfig

            AppLog.d("【统一配置管理器】更新窗口参数: $receiverId/$connectionId")
        }
    }

    /**
     * 🎯 获取批量同步数据
     */
    fun getBatchSyncData(receiverId: String): List<Map<String, Any>> {
        return receiverConfigs[receiverId]?.values?.map { it.toBatchSyncData() } ?: emptyList()
    }

    /**
     * 🎯 新增：更新文字窗口内容
     */
    fun updateTextContent(receiverId: String, connectionId: String, textContent: String, richTextData: String? = null) {
        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        receiverConfigMap[connectionId]?.let { config ->
            config.textContent = textContent
            config.richTextData = richTextData
            AppLog.d("【统一配置管理器】📝 更新文字内容: $receiverId/$connectionId, 内容='$textContent'")
        }
    }

    /**
     * 🎯 新增：更新文字窗口格式
     */
    fun updateTextFormat(
        receiverId: String,
        connectionId: String,
        isBold: Boolean? = null,
        isItalic: Boolean? = null,
        fontSize: Int? = null,
        fontName: String? = null,
        fontFamily: String? = null,
        lineSpacing: Float? = null,
        textAlignment: Int? = null
    ) {
        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        receiverConfigMap[connectionId]?.let { config ->
            isBold?.let { config.isBold = it }
            isItalic?.let { config.isItalic = it }
            fontSize?.let { config.fontSize = it }
            fontName?.let { config.fontName = it }
            fontFamily?.let { config.fontFamily = it }
            lineSpacing?.let { config.lineSpacing = it }
            textAlignment?.let { config.textAlignment = it }
            AppLog.d("【统一配置管理器】📝 更新文字格式: $receiverId/$connectionId")
        }
    }

    /**
     * 🎯 新增：获取文字窗口配置
     */
    fun getTextWindowConfig(receiverId: String, connectionId: String): RemoteWindowConfig? {
        return receiverConfigs[receiverId]?.get(connectionId)?.takeIf {
            connectionId.startsWith("text_")
        }
    }

    /**
     * 🗑️ 删除单个窗口配置
     */
    fun removeWindowConfig(receiverId: String, connectionId: String) {
        val receiverConfigMap = receiverConfigs[receiverId] ?: return

        val removed = receiverConfigMap.remove(connectionId)
        if (removed != null) {
            AppLog.d("【统一配置管理器】已删除窗口配置: $receiverId/$connectionId")
        } else {
            AppLog.w("【统一配置管理器】窗口配置不存在，无法删除: $receiverId/$connectionId")
        }
    }

    /**
     * 🎯 保存配置到本地存储
     */
    fun saveToStorage(context: Context, receiverId: String) {
        try {
            val sharedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val configMap = receiverConfigs[receiverId] ?: return

            val jsonString = gson.toJson(configMap.values.toList())
            sharedPrefs.edit {
                putString("$KEY_PREFIX$receiverId", jsonString)
                putLong("${KEY_PREFIX}${receiverId}_timestamp", System.currentTimeMillis())
            }

            AppLog.d("【统一配置管理器】已保存配置到本地存储: $receiverId")
        } catch (e: Exception) {
            AppLog.e("【统一配置管理器】保存配置失败: $receiverId", e)
        }
    }
}
