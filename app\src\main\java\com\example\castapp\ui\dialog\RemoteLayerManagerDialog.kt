package com.example.castapp.ui.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.Toast
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.castapp.R
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.model.RemoteReceiverConnection
import com.example.castapp.ui.adapter.LayerManagerAdapter
import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

/**
 * 遥控端层级管理BottomSheet对话框
 * 专门用于遥控端调整远程接收端的投屏窗口层级顺序
 */
class RemoteLayerManagerDialog(
    private val remoteReceiverConnection: RemoteReceiverConnection,
    private val windowInfoProvider: () -> List<CastWindowInfo>,
    private val syncStateProvider: () -> Boolean
) : BottomSheetDialogFragment() {

    private lateinit var rvLayerDevices: RecyclerView
    private lateinit var layoutEmptyState: View
    private lateinit var btnClose: ImageButton
    private lateinit var adapter: LayerManagerAdapter
    private lateinit var itemTouchHelper: ItemTouchHelper

    // 对话框关闭回调
    var onDialogDismissed: (() -> Unit)? = null

    companion object {
        fun newInstance(
            remoteReceiverConnection: RemoteReceiverConnection,
            windowInfoProvider: () -> List<CastWindowInfo>,
            syncStateProvider: () -> Boolean
        ): RemoteLayerManagerDialog {
            return RemoteLayerManagerDialog(remoteReceiverConnection, windowInfoProvider, syncStateProvider)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_layer_manager, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews(view)
        setupRecyclerView()
        setupClickListeners()
        refreshDeviceList()

        AppLog.d("【遥控端层级管理】对话框初始化完成 - 设备: ${remoteReceiverConnection.deviceName}")
    }

    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        rvLayerDevices = view.findViewById(R.id.rv_layer_devices)
        layoutEmptyState = view.findViewById(R.id.layout_empty_state)
        btnClose = view.findViewById(R.id.btn_close)
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = LayerManagerAdapter()
        rvLayerDevices.layoutManager = LinearLayoutManager(context)
        rvLayerDevices.adapter = adapter

        // 设置拖拽排序监听器
        adapter.setOnItemMoveListener(object : LayerManagerAdapter.OnItemMoveListener {
            override fun onItemMove(fromPosition: Int, toPosition: Int) {
                AppLog.d("【遥控端层级拖拽】窗口从位置 $fromPosition 移动到 $toPosition")
                // 获取当前排序后的列表
                val currentList = adapter.getCurrentWindowList()
                sendLayerOrderUpdate(currentList)
            }
        })

        // 设置ItemTouchHelper
        val callback = LayerManagerAdapter.ItemTouchHelperCallback(adapter)
        itemTouchHelper = ItemTouchHelper(callback)
        itemTouchHelper.attachToRecyclerView(rvLayerDevices)

        // 设置拖动手柄监听器
        adapter.registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
            override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
                setupDragHandles()
            }
            override fun onItemRangeChanged(positionStart: Int, itemCount: Int) {
                setupDragHandles()
            }
        })
    }

    /**
     * 设置拖动手柄
     */
    private fun setupDragHandles() {
        for (i in 0 until rvLayerDevices.childCount) {
            val viewHolder = rvLayerDevices.getChildViewHolder(rvLayerDevices.getChildAt(i))
            if (viewHolder is LayerManagerAdapter.LayerViewHolder) {
                viewHolder.dragHandle.setOnTouchListener { _, event ->
                    if (event.action == android.view.MotionEvent.ACTION_DOWN) {
                        itemTouchHelper.startDrag(viewHolder)
                    }
                    false
                }
            }
        }
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        btnClose.setOnClickListener {
            AppLog.d("【遥控端层级管理】用户点击关闭按钮")
            dismiss()
        }
    }

    /**
     * 刷新设备列表
     */
    fun refreshDeviceList() {
        try {
            val windowInfoList = windowInfoProvider()
            AppLog.d("【遥控端层级管理】刷新设备列表，共 ${windowInfoList.size} 个设备")

            // 更新适配器数据
            adapter.submitList(windowInfoList)

            // 显示/隐藏空状态
            if (windowInfoList.isEmpty()) {
                rvLayerDevices.visibility = View.GONE
                layoutEmptyState.visibility = View.VISIBLE
                AppLog.d("【遥控端层级管理】显示空状态")
            } else {
                rvLayerDevices.visibility = View.VISIBLE
                layoutEmptyState.visibility = View.GONE
                AppLog.d("【遥控端层级管理】显示设备列表")

                // 延迟设置拖动手柄，确保ViewHolder已创建
                rvLayerDevices.post {
                    setupDragHandles()
                }
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端层级管理】刷新设备列表失败", e)
        }
    }

    /**
     * 🎯 发送层级调整消息到接收端并同时调整本地窗口层级
     */
    private fun sendLayerOrderUpdate(windowOrderList: List<CastWindowInfo>) {
        if (!remoteReceiverConnection.isConnected) {
            AppLog.w("【遥控端层级管理】设备未连接，无法发送层级调整: ${remoteReceiverConnection.deviceName}")
            Toast.makeText(requireContext(), "设备未连接，无法调整层级", Toast.LENGTH_SHORT).show()
            return
        }

        try {
            // 1. 先调整本地窗口层级（无论同步开关状态如何，都要调整本地层级）
            adjustLocalWindowLayers(windowOrderList)

            // 2. 🎯 检查实时同步开关状态
            val isSyncEnabled = syncStateProvider()
            AppLog.d("【遥控端层级管理】检查实时同步开关状态: $isSyncEnabled")

            if (isSyncEnabled) {
                // 实时同步开启，发送层级调整消息到接收端
                AppLog.d("【遥控端层级管理】实时同步已开启，发送层级调整到接收端")

                // 将窗口信息转换为消息格式
                val windowOrderData = windowOrderList.mapIndexed { index, windowInfo ->
                    // 🎥 根本解决方案：模拟摄像头容器ID映射到真实摄像头窗口ID
                    val mappedConnectionId = when {
                        windowInfo.connectionId == "front_camera_placeholder" -> {
                            AppLog.d("【遥控端层级管理】🎥 模拟前置摄像头容器层级调整: ${windowInfo.connectionId} -> front_camera")
                            "front_camera"
                        }
                        windowInfo.connectionId == "rear_camera_placeholder" -> {
                            AppLog.d("【遥控端层级管理】🎥 模拟后置摄像头容器层级调整: ${windowInfo.connectionId} -> rear_camera")
                            "rear_camera"
                        }
                        else -> {
                            windowInfo.connectionId // 普通窗口直接使用原ID
                        }
                    }

                    mapOf(
                        "connection_id" to mappedConnectionId,
                        "order_index" to index,
                        "z_order" to (index + 1) // zOrder从1开始，1是最上层
                    )
                }

                // 创建层级调整消息
                val message = ControlMessage.createRemoteLayerOrderControl(
                    connectionId = remoteReceiverConnection.id,
                    windowOrderList = windowOrderData
                )

                // 通过RemoteReceiverManager发送消息
                val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
                manager.sendWindowTransformControl(remoteReceiverConnection, message)

                AppLog.d("【遥控端层级管理】层级调整消息已发送: ${windowOrderList.size} 个窗口")
                AppLog.d("【遥控端层级管理】层级顺序: ${windowOrderData.joinToString { "${it["connection_id"]} -> 层级${it["z_order"]}" }}")

                Toast.makeText(requireContext(), "层级调整已同步到接收端", Toast.LENGTH_SHORT).show()
            } else {
                // 实时同步关闭，仅调整本地层级
                AppLog.d("【遥控端层级管理】实时同步已关闭，仅调整本地窗口层级")
                Toast.makeText(requireContext(), "层级调整完成（仅本地）", Toast.LENGTH_SHORT).show()
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端层级管理】层级调整失败", e)
            Toast.makeText(requireContext(), "层级调整失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🎯 调整本地窗口层级
     */
    private fun adjustLocalWindowLayers(windowOrderList: List<CastWindowInfo>) {
        try {
            AppLog.d("【遥控端层级管理】开始调整本地可视化窗口层级: ${windowOrderList.size} 个窗口")

            // 获取远程控制对话框的窗口可视化容器
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val controlDialog = manager.getActiveControlDialog(remoteReceiverConnection.id)

            if (controlDialog == null) {
                AppLog.w("【遥控端层级管理】未找到活跃的控制对话框")
                return
            }

            val windowVisualizationView = controlDialog.getWindowVisualizationView()
            if (windowVisualizationView == null) {
                AppLog.w("【遥控端层级管理】未找到窗口可视化容器")
                return
            }

            // 调整可视化窗口的层级
            adjustVisualizationWindowLayers(windowVisualizationView, windowOrderList)

        } catch (e: Exception) {
            AppLog.e("【遥控端层级管理】调整本地窗口层级失败", e)
        }
    }

    /**
     * 🎯 调整可视化窗口层级（参考接收端WindowLayoutModule.adjustWindowLayers的逻辑）
     */
    private fun adjustVisualizationWindowLayers(
        windowVisualizationView: com.example.castapp.ui.view.WindowContainerVisualizationView,
        windowOrderList: List<CastWindowInfo>
    ) {
        try {
            AppLog.d("【遥控端层级管理】开始调整可视化窗口层级，共 ${windowOrderList.size} 个窗口")
            AppLog.d("【遥控端层级管理】列表顺序（从上到下，序号对应视觉层级）：")
            windowOrderList.forEachIndexed { index, windowInfo ->
                AppLog.d("【遥控端层级管理】  序号${index + 1}(最上层=1): ${windowInfo.getDisplayTextWithDevice()}")
            }

            // 🎯 关键修复：参考接收端逻辑，反向遍历列表，从最后一个开始调用 bringToFront()
            // 由于列表顶部显示的是最上层窗口，我们需要反向调整
            // 列表下方的设备先调用 bringToFront()，显示在最下方
            // 列表上方的设备后调用 bringToFront()，显示在最上方
            AppLog.d("【遥控端层级管理】开始反向调整，确保序号1的窗口在最上层:")
            windowOrderList.reversed().forEachIndexed { reverseIndex, windowInfo ->
                val originalIndex = windowOrderList.size - 1 - reverseIndex
                val layerNumber = originalIndex + 1
                val connectionId = windowInfo.connectionId

                // 查找对应的窗口容器View
                val containerView = findWindowContainerView(windowVisualizationView, connectionId) as? com.example.castapp.ui.view.WindowVisualizationContainerView
                if (containerView != null) {
                    // 🎯 关键：使用bringToFront调整层级，这会同时处理窗口和边框
                    containerView.bringToFront()
                    AppLog.d("【遥控端层级管理】调整序号${layerNumber}窗口: ${windowInfo.getDisplayTextWithDevice()}")
                } else {
                    AppLog.w("【遥控端层级管理】未找到序号${layerNumber}窗口: $connectionId")
                }
            }

            AppLog.d("【遥控端层级管理】可视化窗口层级调整完成！序号1的窗口应该在最上层，序号越大越在下层")

            // 🎯 关键修复：更新可视化数据中的zOrder值，确保同步时发送正确的层级信息
            updateVisualizationDataZOrder(windowVisualizationView, windowOrderList)

        } catch (e: Exception) {
            AppLog.e("【遥控端层级管理】调整可视化窗口层级失败", e)
        }
    }



    /**
     * 🎯 更新可视化数据中的zOrder值
     */
    private fun updateVisualizationDataZOrder(
        windowVisualizationView: com.example.castapp.ui.view.WindowContainerVisualizationView,
        windowOrderList: List<CastWindowInfo>
    ) {
        try {
            AppLog.d("【遥控端层级管理】开始更新可视化数据中的zOrder值")

            // 获取当前可视化数据列表
            val currentVisualizationDataList = windowVisualizationView.getVisualizationDataList().toMutableList()

            // 根据新的层级顺序更新zOrder
            windowOrderList.forEachIndexed { index, windowInfo ->
                val newZOrder = index + 1 // zOrder从1开始，1是最上层
                val connectionId = windowInfo.connectionId

                // 查找并更新对应的可视化数据
                val dataIndex = currentVisualizationDataList.indexOfFirst { it.connectionId == connectionId }
                if (dataIndex >= 0) {
                    val oldData = currentVisualizationDataList[dataIndex]
                    val updatedData = oldData.copy(zOrder = newZOrder)
                    currentVisualizationDataList[dataIndex] = updatedData

                    AppLog.d("【遥控端层级管理】更新zOrder: $connectionId, 旧值=${oldData.zOrder} -> 新值=$newZOrder")
                } else {
                    AppLog.w("【遥控端层级管理】未找到可视化数据: $connectionId")
                }
            }

            // 更新可视化数据列表
            windowVisualizationView.updateVisualizationData(currentVisualizationDataList)
            AppLog.d("【遥控端层级管理】可视化数据zOrder更新完成")

        } catch (e: Exception) {
            AppLog.e("【遥控端层级管理】更新可视化数据zOrder失败", e)
        }
    }

    /**
     * 🎯 查找窗口容器View
     */
    private fun findWindowContainerView(
        windowVisualizationView: com.example.castapp.ui.view.WindowContainerVisualizationView,
        connectionId: String
    ): android.view.View? {
        try {
            // 遍历WindowContainerVisualizationView的子View
            for (i in 0 until windowVisualizationView.childCount) {
                val childView = windowVisualizationView.getChildAt(i)

                // 检查是否是WindowVisualizationContainerView
                if (childView is com.example.castapp.ui.view.WindowVisualizationContainerView) {
                    val windowData = childView.getWindowData()
                    if (windowData?.connectionId == connectionId) {
                        return childView
                    }
                }
            }

            return null

        } catch (e: Exception) {
            AppLog.e("【遥控端层级管理】查找窗口容器View失败: $connectionId", e)
            return null
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDialogDismissed?.invoke()
        AppLog.d("【遥控端层级管理】对话框已关闭")
    }
}
