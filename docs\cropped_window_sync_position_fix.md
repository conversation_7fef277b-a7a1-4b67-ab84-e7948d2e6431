# 裁剪窗口同步位置修复方案

## 问题描述

在接收端添加窗口A，裁剪窗口A后，将遥控端连接接收端，遥控端远程接收端控制窗口上生成了裁剪后的窗口A。在关闭遥控端"实时同步"开关，将遥控端窗口A移动到M点，点击"同步"按钮，发现接收端裁剪后的窗口A没有移动到M点。

## 根本原因分析

### 问题流程：
1. **接收端错误发送**：接收端发送给遥控端的窗口信息中 `isCropping=false`（明明是裁剪窗口）
2. **遥控端接收错误信息**：遥控端基于错误的 `isCropping=false` 进行处理
3. **接收端错误处理**：接收端收到同步请求后，按照非裁剪窗口处理
4. **重复裁剪处理**：接收端在 `applyWindowParameters` 方法中又调用了 `applyRemoteCrop`
5. **位置偏移**：最终窗口位置被重复偏移

### 关键问题：
1. **接收端发送错误的裁剪状态**：使用了 `isCroppingMode()` 而不是 `isCroppedWindow()`
2. **接收端重复处理裁剪**：对于裁剪窗口又调用了 `applyRemoteCrop`

### 两个方法的区别：
- `isCroppingMode()`：返回是否正在裁剪模式（用户正在编辑裁剪）
- `isCroppedWindow()`：返回窗口是否已被裁剪（有裁剪区域）

## 修复方案

### 修复1：遥控端使用正确的窗口尺寸
**文件**：`app/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.kt`
**方法**：`parseWindowInfoList`

**修复内容**：
```kotlin
// 🎯 关键修复：使用原始窗口尺寸，裁剪效果由Canvas实现
val baseWindowWidth = (windowData["baseWindowWidth"] as? Number)?.toInt() ?: 0
val baseWindowHeight = (windowData["baseWindowHeight"] as? Number)?.toInt() ?: 0

// 在CastWindowInfo构造中：
baseWindowWidth = baseWindowWidth,
baseWindowHeight = baseWindowHeight
```

**修复逻辑**：
- 不再使用 `actualDisplayWidth/Height`（裁剪后尺寸）
- 改用原始的 `baseWindowWidth/Height`
- 裁剪效果完全由Canvas裁剪实现

### 修复2：接收端发送正确的裁剪状态
**文件**：`app/src/main/java/com/example/castapp/manager/windowsettings/WindowInfoModule.kt`
**方法**：`createMediaWindowInfo`, `createCameraWindowInfo`, 投屏窗口信息创建

**修复内容**：
```kotlin
// 获取裁剪区域参数和裁剪窗口状态
val cropRectRatio = transformHandler.getCropRectRatio()
val isCropping = cropRectRatio != null  // 🎯 关键修复：根据裁剪区域是否存在判断是否为裁剪窗口
```

**修复逻辑**：
- 不再使用 `isCroppingMode()`（是否正在裁剪模式）
- 改用 `cropRectRatio != null`（是否有裁剪区域）来判断窗口是否被裁剪

### 修复3：接收端避免重复裁剪处理
**文件**：`app/src/main/java/com/example/castapp/remote/RemoteReceiverControlServer.kt`
**方法**：`applyWindowParameters`

**修复内容**：
```kotlin
// 🎯 关键修复：只有非裁剪窗口才需要单独应用裁剪参数
// 裁剪窗口的裁剪状态已经在一次性变换中处理，避免重复处理导致位置偏移
if (!isCropping) {
    @Suppress("UNCHECKED_CAST")
    val cropRectData = windowData["cropRectRatio"] as? Map<String, Any>
    if (cropRectData != null) {
        val left = (cropRectData["left"] as? Number)?.toFloat() ?: 0f
        val top = (cropRectData["top"] as? Number)?.toFloat() ?: 0f
        val right = (cropRectData["right"] as? Number)?.toFloat() ?: 1f
        val bottom = (cropRectData["bottom"] as? Number)?.toFloat() ?: 1f
        val cropRatio = RectF(left, top, right, bottom)
        windowSettingsManager.applyRemoteCrop(connectionId, cropRatio)
        AppLog.d("【远程控制服务器】🎯 非裁剪窗口单独应用裁剪: $connectionId")
    }
} else {
    AppLog.d("【远程控制服务器】🎯 裁剪窗口跳过重复裁剪处理: $connectionId")
}
```

**修复逻辑**：
1. **裁剪窗口**：跳过单独的裁剪处理，因为已经在 `applyPrecisionTransform` 中一次性处理
2. **非裁剪窗口**：继续使用原有的单独裁剪处理逻辑
3. **避免重复**：防止裁剪状态被重复应用导致位置偏移

## 测试方案

### 测试步骤：
1. 在接收端添加一个图片窗口A
2. 对窗口A进行裁剪操作
3. 启动遥控端，连接到接收端
4. 在遥控端关闭"实时同步"开关
5. 将遥控端的窗口A移动到新位置M点
6. 点击"同步"按钮
7. 观察接收端的窗口A是否正确移动到M点

### 预期结果：
- 接收端的裁剪窗口A应该正确移动到M点
- 不应该出现位置偏移
- 日志中应该显示"裁剪窗口跳过重复裁剪处理"

### 验证日志：
遥控端日志应显示：
```
🔄 同步按钮被点击 - 检查连接状态: true
【统一配置管理器】🎯 同步裁剪参数: front_camera, isCropping=true, cropRectRatio=RectF(...)
```

接收端日志应显示：
```
【远程控制服务器】🎯 裁剪窗口一次性变换: front_camera -> 位置=(x, y)
【远程控制服务器】🎯 裁剪窗口跳过重复裁剪处理: front_camera
```

## 相关文件

- `RemoteReceiverControlServer.kt` - 接收端控制服务器（主要修复）
- `TransformManager.kt` - 变换管理器
- `WindowSettingsManager.kt` - 窗口设置管理器
- `RemoteWindowConfig.kt` - 遥控端配置管理

## 注意事项

1. 此修复只影响批量同步中的裁剪窗口处理
2. 不影响非裁剪窗口的正常功能
3. 不影响实时同步功能
4. 保持向后兼容性
